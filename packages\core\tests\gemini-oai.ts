import { generateText, tool } from "xsai";
import { createGoogleGenerativeAI } from "@xsai-ext/providers-cloud";
import { ProxyAgent, fetch } from "undici";
import { Schema } from "koishi";

const provider = createGoogleGenerativeAI("AIzaSyAUYovCNFte3hBpMAjwPYhYLNMeKOWQZNY");

const ufetch = (async (input, init) => {
    const proxy = "http://127.0.0.1:7897";

    init = { ...init, dispatcher: new ProxyAgent(proxy) };

    return await fetch(input, init);
}) as unknown as typeof globalThis.fetch;

const { text, messages } = await generateText({
    ...provider.chat("gemini-2.5-flash"),
    // fetch: ufetch,
    tools: [
        {
            type: "function",
            function: {
                name: "get_weather",
                description: "Get the weather in a given location",
                parameters: {
                    type: "object",
                    properties: {
                        location: {
                            type: "string",
                            description: "The city and state, e.g. Chicago, IL",
                        },
                        unit: { type: "string", enum: ["celsius", "fahrenheit"] },
                    },
                    required: ["location"],
                },
            },
            execute(input, options) {
                console.log("Executing get_weather:", input);
                return { status: "success", result: "天气晴朗" };
            },
        },
    ],
    messages: [
        {
            role: "system",
            content: "You are a helpful assistant.",
        },
        {
            role: "user",
            content: "今天上海天气怎么样？",
        },
    ],
});

console.log(text);
console.log(messages);
