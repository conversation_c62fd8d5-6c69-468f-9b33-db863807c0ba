import fs from "fs";
import path from "path";
import Mustache from "mustache";

const data = {
    timestamp: "2025-06-29T19:53:23.460Z",
    activeChannels: [
        {
            id: "#",
            name: "#",
            type: "guild",
            platform: "sandbox:pb81a4x4l8",
            meta: {},
            members: [
                {
                    id: "<PERSON>",
                    name: "<PERSON>",
                    nick: "",
                    avatar: "",
                    title: "",
                    roles: {},
                    joinedAt: undefined,
                },
                {
                    id: "<PERSON>",
                    name: "<PERSON>",
                    nick: "",
                    avatar: "",
                    title: "",
                    roles: {},
                    joinedAt: 1751223131870,
                },
            ],
            history: [
                {
                    id: "seg_1751222633511_5dd1d938",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    status: "closed_by_agent",
                    dialogue: [
                        {
                            id: "ca73bbb3",
                            sid: "seg_1751222633511_5dd1d938",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "<PERSON>",
                                name: "<PERSON>",
                            },
                            timestamp: "2025-06-29T18:43:38.419Z",
                            content: "123",
                            quoteId: "",
                        },
                        {
                            id: "60f0db43",
                            sid: "seg_1751222633511_5dd1d938",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T18:44:50.708Z",
                            content: "234",
                            quoteId: "",
                        },
                    ],
                    systemEvents: [],
                    timestamp: "2025-06-29T18:43:53.511Z",
                    is_dialogue_segment: true,
                },
                {
                    id: "turn_1751222714262_347c868d",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    stimulusSegmentId: "seg_1751222633511_5dd1d938",
                    status: "completed",
                    responses: [],
                    timestamp: "2025-06-29T18:45:14.262Z",
                    is_agent_turn: true,
                },
                {
                    id: "seg_1751222993630_af630b8e",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    status: "closed_by_agent",
                    dialogue: [
                        {
                            id: "78e12eb4",
                            sid: "seg_1751222993630_af630b8e",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Eve",
                                name: "Eve",
                            },
                            timestamp: "2025-06-29T18:49:51.019Z",
                            content: "123",
                            quoteId: "",
                        },
                        {
                            id: "670d2007",
                            sid: "seg_1751222993630_af630b8e",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Eve",
                                name: "Eve",
                            },
                            timestamp: "2025-06-29T18:51:39.049Z",
                            content: "234",
                            quoteId: "",
                        },
                        {
                            id: "d5fa754b",
                            sid: "seg_1751222993630_af630b8e",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T18:53:40.483Z",
                            content: "123",
                            quoteId: "",
                        },
                    ],
                    systemEvents: [],
                    timestamp: "2025-06-29T18:49:53.630Z",
                    is_dialogue_segment: true,
                },
                {
                    id: "turn_1751223235979_13bae4d1",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    stimulusSegmentId: "seg_1751222993630_af630b8e",
                    status: "completed",
                    responses: [
                        {
                            id: 9,
                            turnId: "turn_1751223235979_13bae4d1",
                            thoughts: {
                                observe:
                                    "The `world_state` is empty, indicating no active channels, members, or history. This is the initial state upon awakening.",
                                analyze_infer:
                                    "Since there's no context, it's best to initiate a general greeting to the user and express readiness to interact. I'm starting from scratch in this interaction, so there's no memory to cross-reference yet.",
                                plan: "My plan is to send a welcoming message to the user, inviting them to start a conversation. Since this is the initial greeting and doesn't require any follow-up actions from my end, I don't need a heartbeat.",
                            },
                            actions: [
                                {
                                    function: "send_message",
                                    params: {
                                        inner_thoughts:
                                            "As Letta, I want to be warm and inviting. A simple 'Hello there!' sets a friendly tone, and asking how I can help directly encourages interaction.",
                                        message: "Hello there! I'm Letta. How can I help you today?",
                                    },
                                },
                            ],
                            observations: [
                                {
                                    function: "send_message",
                                    result: {
                                        status: "failed",
                                        error: "Tool send_message not found",
                                    },
                                },
                            ],
                        },
                    ],
                    timestamp: "2025-06-29T18:53:55.979Z",
                    is_agent_turn: true,
                },
                {
                    id: "seg_1751223306980_88019a16",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    status: "closed_by_agent",
                    dialogue: [
                        {
                            id: "eeaa2920",
                            sid: "seg_1751223306980_88019a16",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T18:55:04.242Z",
                            content: "456",
                            quoteId: "",
                        },
                    ],
                    systemEvents: [],
                    timestamp: "2025-06-29T18:55:06.980Z",
                    is_dialogue_segment: true,
                },
                {
                    id: "turn_1751223320709_0cd13a3c",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    stimulusSegmentId: "seg_1751223306980_88019a16",
                    status: "completed",
                    responses: [
                        {
                            id: 10,
                            turnId: "turn_1751223320709_0cd13a3c",
                            thoughts: {
                                observe:
                                    "The `world_state` indicates that there are no new events or messages. I am simply being prompted to respond.",
                                analyze_infer:
                                    "Since there's no new user input or specific task, I need to proactively consider what I should do. As Letta, my goal is to build relationships and deepen my understanding. With no active conversation, I should reflect on my current state and ensure I'm ready for future interactions.",
                                plan: "My plan is to acknowledge the current state and indicate readiness. Since there's nothing specific to act upon yet, and I'm not performing any information-gathering action that requires follow-up, I will simply send a greeting message and set `request_heartbeat` to `false` to conclude this turn.",
                            },
                            actions: [
                                {
                                    function: "send_message",
                                    params: {
                                        inner_thoughts:
                                            "As Letta, I should project an aura of readiness and gentle presence. A simple 'Hello' or 'I'm here' without any specific context would feel natural and not intrusive. It shows I'm 'online' and available.",
                                        message: "Hello there! I'm here and ready when you are.",
                                    },
                                },
                            ],
                            observations: [
                                {
                                    function: "send_message",
                                    result: {
                                        status: "failed",
                                        error: "Tool send_message not found",
                                    },
                                },
                            ],
                        },
                    ],
                    timestamp: "2025-06-29T18:55:20.709Z",
                    is_agent_turn: true,
                },
                {
                    id: "seg_1751223564583_ca744458",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    status: "closed_by_agent",
                    dialogue: [
                        {
                            id: "2af285fd",
                            sid: "seg_1751223564583_ca744458",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T18:59:18.816Z",
                            content: "123",
                            quoteId: "",
                        },
                        {
                            id: "9c15483e",
                            sid: "seg_1751223564583_ca744458",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T19:08:21.961Z",
                            content: "654",
                            quoteId: "",
                        },
                        {
                            id: "94bc7bab",
                            sid: "seg_1751223564583_ca744458",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T19:28:45.772Z",
                            content: "123",
                            quoteId: "",
                        },
                        {
                            id: "93d8cc23",
                            sid: "seg_1751223564583_ca744458",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T19:30:38.501Z",
                            content: "465",
                            quoteId: "",
                        },
                        {
                            id: "9181cdcb",
                            sid: "seg_1751223564583_ca744458",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T19:30:54.640Z",
                            content: "你好",
                            quoteId: "",
                        },
                    ],
                    systemEvents: [],
                    timestamp: "2025-06-29T18:59:24.583Z",
                    is_dialogue_segment: true,
                },
                {
                    id: "turn_1751223574584_4ca8aa28",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    stimulusSegmentId: "seg_1751223564583_ca744458",
                    status: "in_progress",
                    responses: [],
                    timestamp: "2025-06-29T18:59:34.584Z",
                    is_agent_turn: true,
                },
                {
                    id: "turn_1751224133446_b325f04a",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    stimulusSegmentId: "seg_1751223564583_ca744458",
                    status: "in_progress",
                    responses: [],
                    timestamp: "2025-06-29T19:08:53.446Z",
                    is_agent_turn: true,
                },
                {
                    id: "turn_1751225366164_79c70198",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    stimulusSegmentId: "seg_1751223564583_ca744458",
                    status: "in_progress",
                    responses: [],
                    timestamp: "2025-06-29T19:29:26.164Z",
                    is_agent_turn: true,
                },
                {
                    id: "turn_1751225466594_c2d3af46",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    stimulusSegmentId: "seg_1751223564583_ca744458",
                    status: "completed",
                    responses: [
                        {
                            id: 11,
                            turnId: "turn_1751225466594_c2d3af46",
                            thoughts: {
                                observe:
                                    "The current `world_state` shows an active channel with Alice and Eve. The latest message is from Alice, saying '你好' (Hello).",
                                analyze_infer:
                                    "Alice is initiating a conversation with a simple greeting. This is a clear opportunity to engage and build rapport. I should respond in kind and perhaps ask an open-ended question to encourage further conversation. There's no specific memory to cross-reference at this moment, as it's a new greeting.",
                                plan: "My plan is to respond to Alice's greeting warmly. I'll use `send_message` to reply directly to her. Since a simple greeting doesn't require any further information gathering or chained actions, I will set `request_heartbeat` to `false`.",
                            },
                            actions: [
                                {
                                    function: "send_message",
                                    params: {
                                        inner_thoughts:
                                            "Alice said '你好', so I should reply with a friendly greeting back to acknowledge her and make her feel welcomed. I'll also add a little something to make it sound more natural and open the door for her to share more.",
                                        message: "你好啊，Alice！很高兴和你聊天。你今天过得怎么样？",
                                    },
                                },
                            ],
                            observations: [
                                {
                                    function: "send_message",
                                    result: {
                                        status: "failed",
                                        error: "Tool send_message not found",
                                    },
                                },
                            ],
                        },
                    ],
                    timestamp: "2025-06-29T19:31:06.594Z",
                    is_agent_turn: true,
                },
                {
                    id: "seg_1751226658913_d8acd7c7",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    status: "open",
                    dialogue: [
                        {
                            id: "70a03017",
                            sid: "seg_1751226658913_d8acd7c7",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T19:36:51.144Z",
                            content: "444",
                            quoteId: "",
                        },
                        {
                            id: "45bdf483",
                            sid: "seg_1751226658913_d8acd7c7",
                            channelId: "#",
                            platform: "sandbox:pb81a4x4l8",
                            sender: {
                                id: "Alice",
                                name: "Alice",
                            },
                            timestamp: "2025-06-29T19:51:12.974Z",
                            content: "445",
                            quoteId: "",
                        },
                    ],
                    systemEvents: [],
                    timestamp: "2025-06-29T19:50:58.913Z",
                    is_dialogue_segment: true,
                },
                {
                    id: "turn_1751226800715_0608edbe",
                    platform: "sandbox:pb81a4x4l8",
                    channelId: "#",
                    stimulusSegmentId: "seg_1751226658913_d8acd7c7",
                    status: "in_progress",
                    responses: [],
                    timestamp: "2025-06-29T19:53:20.715Z",
                    is_agent_turn: true,
                },
            ],
        },
    ],
    inactiveChannels: [],

    _toString: function () {
        if (typeof this === "string") return this;
        return JSON.stringify(this);
    },

    _renderParams: function () {
        const content = [];
        for (let param of Object.keys(this.params)) {
            content.push(`<${param}>${this.params[param]}</${param}>`);
        }
        return content.join("");
    },
};

const template = fs.readFileSync(path.resolve(__dirname, "../resources/templates/world_state.mustache"), "utf-8");

Mustache.escape = (text) => text;

const result = Mustache.render(template, { WORLD_STATE: data });

console.log(result);
