import { generateText, tool } from "xsai";
import { createGoogleGenerativeAI } from "@xsai-ext/providers-cloud";
import fs from "fs";
import { message } from "xsai";

const provider = createGoogleGenerativeAI("AIzaSyAUYovCNFte3hBpMAjwPYhYLNMeKOWQZNY");

const logo = fs.readFileSync("D:\\Codespace\\koishi-app\\external\\YesImBot\\img\\logo.png", "base64");
const img2 = fs.readFileSync("D:\\Codespace\\koishi-app\\external\\YesImBot\\img\\screenshot-1.png", "base64");

const { text, messages } = await generateText({
    ...provider.chat("gemini-2.5-flash"),

    messages: [
        {
            role: "system",
            content:
                "你是一位语言学家，擅长从对话中总结信息。你将接收到一个来自群聊的对话片段，包含文本和图片内容。对话中出现的图片会被集中提供，并用 'Image #[ID]:' 的格式进行编号。在随后的对话文本中，会使用 <image id=\"[ID]\"> 的占位符来引用这些图片。请你根据现有内容回答问题。",
        },
        {
            role: "user",
            content: `
Alice: <image id="1"/>
Alice: 你们认为这个logo怎么样
Bob: 太酷了
Charlie: 哈哈，我喜欢
Alice: 那就用这个了
Koishi: <image id="2"/>
Koishi: 笑死我了
            `,
        },
        {
            role: "user",
            content: [message.textPart("Image #1: "), message.imagePart(`data:image/png;base64,${logo}`)],
        },
        {
            role: "user",
            content: [message.textPart("Image #2: "), message.imagePart(`data:image/png;base64,${img2}`)],
        },
        {
            role: "user",
            content: "上面的对话片段在讨论什么内容",
        },
    ],
});

console.log(text);
console.log(messages);
