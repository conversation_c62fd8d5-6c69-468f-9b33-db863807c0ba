{{#WORLD_STATE}}
<world_state timestamp="{{timestamp}}">
  {{#activeChannels}}
  <channel id="{{id}}" type="{{type}}" platform="{{platform}}">
    <name>{{name}}</name>
    {{#meta.description}}
    <description>{{.}}</description>
    {{/meta.description}}
    {{#members.length}}
    <members>
      {{#members}}
      <user id="{{id}}"{{#nick}} nick="{{.}}"{{/nick}}{{#roles}} roles="{{_toString}}"{{/roles}}>{{name}}</user>
      {{/members}}
    </members>
    {{/members.length}}
    <history>
      {{#history}}
      {{#is_dialogue_segment}}
      <dialogue status="{{status}}" timestamp="{{timestamp}}">
        {{#dialogue}}
        [{{id}}] {{timestamp}} {{sender.name}}: {{content}}
        {{/dialogue}}
      </dialogue>
      {{#systemEvents.length}}
      <system_events>
        {{#systemEvents}}
        <event type="{{type}}" timestamp="{{timestamp}}">{{payload}}</event>
        {{/systemEvents}}
      </system_events>
      {{/systemEvents.length}}
      {{/is_dialogue_segment}}
      {{#is_agent_turn}}
      {{#responses.length}}
      <agent_turn status="{{status}}" timestamp="{{timestamp}}">
        {{#responses}}
        <response>
        <thoughts>
          <observe>{{thoughts.observe}}</observe>
          <analyze_infer>{{thoughts.analyze_infer}}</analyze_infer>
          <plan>{{thoughts.plan}}</plan>
        </thoughts>
        <actions>
          {{#actions}}
          <action id="{{function}}">
            <function>{{function}}</function>
            <params>{{_renderParams}}</params>
          </action>
          {{/actions}}
        </actions>
        <observations>
          {{#observations}}
          <observation id="{{function}}">
            <function>{{function}}</function>
            <status>{{status}}</status>
            {{#result}}
            <result>{{_toString}}</result>
            {{/result}}
            {{#error}}
            <error>{{_toString}}</error>
            {{/error}}
          </observation>
          {{/observations}}
        </observations>
        </response>
        {{/responses}}
      </agent_turn>
      {{/responses.length}}
      {{/is_agent_turn}}
      {{/history}}
    </history>
  </channel>
  {{/activeChannels}}
  {{#inactiveChannels}}
  <inactive_channels>
    <channel_id>{{id}}</channel_id>
  </inactive_channels>
  {{/inactiveChannels}}
</world_state>
{{/WORLD_STATE}}