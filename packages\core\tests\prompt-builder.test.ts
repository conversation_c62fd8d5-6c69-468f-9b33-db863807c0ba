///<reference types="bun-types" />

import { expect, test, mock, beforeEach } from "bun:test";
import { Context } from "koishi";
import { PromptBuilder, PromptContext } from "../src/agent/prompt-builder";
import { DialogueSegment, WorldState } from "../src/services";

// Mock a minimal Koishi context
const mockContext = {
    logger: (name: string) => ({
        info: mock(),
        warn: mock(),
        error: mock(),
        debug: mock(),
    }),
} as any as Context;

// A default mock context for the prompt builder
const createMockPromptContext = (): PromptContext => ({
    toolSchemas: [
        { name: "test_tool", description: "A test tool" }
    ],
    worldState: {
        timestamp: new Date().toISOString(),
        activeChannels: [],
        inactiveChannels: [],
    } as WorldState,
    currentSegment: {
        id: "seg-1",
        platform: "test",
        channelId: "ch-1",
        status: "open",
        events: [
            {
                id: "evt-1",
                type: "message",
                timestamp: new Date(),
                payload: {
                    actor: { id: "user-1", name: "TestUser" },
                    content: "Hello, agent!",
                    messageId: "msg-1",
                },
                is_message: true,
            },
        ],
        startTimestamp: new Date(),
        endTimestamp: new Date(),
        is_dialogue_segment: true,
        is_agent_turn: false,
    } as DialogueSegment,
    agentState: {
        lifeCycleStatus: "active",
        analysis: { isEngaging: true, isQuestion: false, mentionsAgent: true },
        willingness: { value: 0.9, threshold: 0.5, shouldAct: true, reasons: ["Mentioned"] },
    },
    agentTurnHistory: [],
});

test("PromptBuilder should correctly build prompts with given context", async () => {
    const config = {
        SystemTemplate: "System: {{WORLD_STATE.timestamp}} Tools: {{#TOOL_DEFINITION.tools}}{{name}}{{/TOOL_DEFINITION.tools}}",
        UserTemplate: "User: {{CURRENT_CONVERSATION.segment.events.0.payload.content}}",
    };

    // Mock the file reading for partials
    mock.module("fs", () => ({
        readFileSync: mock(() => "Default partial content"),
    }));

    const promptBuilder = new PromptBuilder(mockContext, config);
    const promptContext = createMockPromptContext();

    const result = await promptBuilder.build(promptContext);

    expect(result.system).toContain("System:");
    expect(result.system).toContain(promptContext.worldState.timestamp);
    expect(result.system).toContain("test_tool");
    expect(result.user).toBe("User: Hello, agent!");
});

test("PromptBuilder should correctly render partials", async () => {
    const config = {
        SystemTemplate: "System: {{>MY_PARTIAL}}",
        UserTemplate: "User: test",
    };

    const promptBuilder = new PromptBuilder(mockContext, config);
    promptBuilder.registerPartial("MY_PARTIAL", "Partial content with {{AGENT_SELF_ASSESSMENT.willingness.value}}");
    
    const promptContext = createMockPromptContext();
    const result = await promptBuilder.build(promptContext);

    expect(result.system).toBe("System: Partial content with 0.9");
});

test("PromptBuilder should handle complex nested data in templates", async () => {
    const config = {
        SystemTemplate: "Actor: {{CURRENT_CONVERSATION.segment.events.0.payload.actor.name}}",
        UserTemplate: "Analysis: Engaging? {{AGENT_SELF_ASSESSMENT.analysis.isEngaging}}",
    };

    const promptBuilder = new PromptBuilder(mockContext, config);
    const promptContext = createMockPromptContext();

    const result = await promptBuilder.build(promptContext);

    expect(result.system).toBe("Actor: TestUser");
    expect(result.user).toBe("Analysis: Engaging? true");
});

beforeEach(() => {
    mock.restore();
});
